import { 
    AuthUrls, 
    WidgetConfig,
    ApiResponse,
    AuthResponse,
    SignupSuccessResponse, 
    SignupErrorResponse, 
    SignupResponse,
    SigninSuccessResponse,
    SigninErrorResponse,
    SigninResponse,
    ResendConfirmationResponse
} from './lib/types';
import { WidgetCustomization } from './lib/customization-types';
import { Modal } from './components/Modal';
import { AuthFrame } from './components/AuthFrame';
import { ApiService } from './lib/api';
import { EventManager } from './lib/eventManager';
import { getStyleContent, getComponentStyles, generatePageLayoutStyles, generateTermsContainerStyles } from './lib/styles';
import { StyleGenerator } from './lib/style-generator';
import './styles/default-theme.css';

declare global {
    interface Window {
        AuthiqaGlobalConfig?: {
            customization?: Partial<WidgetCustomization>;
            messages?: Record<string, string>;
        };
        _authiqaGoogleOneTapDismissed?: boolean;
        _authiqaGoogleOneTapPromptActive?: boolean;
    }
}


export class AuthiqaWidget {
    private config: WidgetConfig;
    private authUrls: AuthUrls | null = null;
    private api: ApiService; // Reintroducing the api property
    private currentAction: keyof AuthUrls | null = null; // Track the current action for custom messages
    private googleSsoConfig?: { enabled: boolean; clientId: string }; // Store Google SSO config

    constructor(config: WidgetConfig) {
        // Merge global config with provided config
        if (window.AuthiqaGlobalConfig) {
            // Merge customization
            if (window.AuthiqaGlobalConfig.customization) {
                config.customization = {
                    ...window.AuthiqaGlobalConfig.customization,
                    ...config.customization
                };
            }
            
            // Merge messages
            if (window.AuthiqaGlobalConfig.messages) {
                config.messages = {
                    ...window.AuthiqaGlobalConfig.messages,
                    ...config.messages
                };
            }
        }
        
        this.config = config;
        this.api = new ApiService(config); // Initialize the ApiService
        this.injectStyles(); // Add this line
    }

    getAuthUrls(): AuthUrls {
        if (!this.authUrls) {
            throw new Error('Widget not initialized. Call initialize() first.');
        }
        return this.authUrls;
    }

    async initialize(): Promise<void> {
        try {
            const response = await this.api.getOrganizationDetails();
            this.authUrls = response.authUrls;
            // Store googleSsoConfig if present
            if (response.googleSsoConfig) {
                this.googleSsoConfig = response.googleSsoConfig;
            }

            // Use optional chaining with default to true for backward compatibility
            if (response.domainRestrictionEnabled ?? true) {
                const isValidDomain = this.validateDomain(response.organizationUrl);
                if (!isValidDomain) {
                    this.showUnauthorizedError();
                    return;
                }
            }
            // Rest of the initialization code...

            if (this.currentAction === 'signin') {
                this.renderSignInForm();
            }
        } catch (error) {
            console.warn('Failed to fetch organization details:', error);
            // Set default authUrls instead of throwing
            const apiBase = this.api.getApiBase();
            this.authUrls = {
                signin: `${apiBase}/auth/signin`,
                signup: `${apiBase}/auth/signup`,
                verify: `${apiBase}/auth/verify`,
                reset: `${apiBase}/auth/reset`,
                update: `${apiBase}/auth/update`,
                resend: `${apiBase}/auth/resend`,
                successful: `${apiBase}/auth/successful`
            };
        }
    }

    show(action: keyof AuthUrls): void {
        this.currentAction = action;

        // Show the form immediately
        if (action === 'verify') {
            this.handleEmailVerification();
        } else if (action === 'signin') {
            this.renderSignInForm();
        } else if (action === 'signup') {
            this.renderSignUpForm();
        } else if (action === 'reset') {
            this.renderResetPasswordForm();
        } else if (action === 'update') {
            this.renderUpdatePasswordForm();
        } else if (action === 'resend') {
            this.renderResendConfirmationForm();
        }

        // Initialize in background if not already done
        if (!this.authUrls) {
            this.initialize().catch(error => {
                console.warn('Failed to fetch organization details:', error);
            });
        }
    }

    private initializeContainer(): HTMLElement {
        let container = document.getElementById(this.config.container);
        
        if (!container) {
            container = document.createElement('div');
            container.id = this.config.container;
            document.body.appendChild(container);
        }

        // Apply base container class
        container.className = 'authiqa-container';

        // Apply page layout customizations if present
        if (this.config.customization?.pageLayout) {
            const pageLayout = this.config.customization.pageLayout;
            
            // Apply background color to body
            if (pageLayout.backgroundColor) {
                document.body.style.backgroundColor = pageLayout.backgroundColor;
            }

            // Apply positioning styles to container
            if (pageLayout.formPosition) {
                document.body.style.display = 'flex';
                document.body.style.minHeight = '100vh';
                
                // Set alignment based on position
                switch (pageLayout.formPosition) {
                    case 'top':
                        document.body.style.alignItems = 'flex-start';
                        document.body.style.justifyContent = 'center';
                        break;
                    case 'bottom':
                        document.body.style.alignItems = 'flex-end';
                        document.body.style.justifyContent = 'center';
                        break;
                    case 'left':
                        document.body.style.alignItems = 'center';
                        document.body.style.justifyContent = 'flex-start';
                        break;
                    case 'right':
                        document.body.style.alignItems = 'center';
                        document.body.style.justifyContent = 'flex-end';
                        break;
                    default: // center
                        document.body.style.alignItems = 'center';
                        document.body.style.justifyContent = 'center';
                }
            }

            // Apply margins to container
            if (pageLayout.formMarginTop) container.style.marginTop = pageLayout.formMarginTop;
            if (pageLayout.formMarginBottom) container.style.marginBottom = pageLayout.formMarginBottom;
            if (pageLayout.formMarginLeft) container.style.marginLeft = pageLayout.formMarginLeft;
            if (pageLayout.formMarginRight) container.style.marginRight = pageLayout.formMarginRight;
        }

        // Only apply theme if no customization is present
        if (!this.config.customization && !this.config.disableStyles) {
            if (this.config.theme === 'dark') {
                document.body.setAttribute('data-theme', 'dark');
            } else {
                document.body.removeAttribute('data-theme');
            }

            if (this.config.theme !== 'none') {
                container.setAttribute('data-theme', this.config.theme || 'light');
            }
        }

        return container;
    }

    private createLabeledInput(
        type: string,
        id: string,
        placeholder: string,
        labelText: string,
        required: boolean = true
    ): { container: HTMLDivElement; input: HTMLInputElement } {
        const container = document.createElement('div');
        container.className = 'labeled-input-container';
        container.classList.add('authiqa-labeled-input');

        // Create label
        const label = document.createElement('label');
        label.setAttribute('for', `authiqa-${id}`);
        label.textContent = labelText;
        label.classList.add('authiqa-label');
        
        // Create input
        const input = document.createElement('input');
        input.setAttribute('type', type);
        input.setAttribute('id', `authiqa-${id}`);
        input.setAttribute('name', id);
        input.setAttribute('placeholder', placeholder);
        input.setAttribute('required', required ? 'true' : 'false');
        input.classList.add('authiqa-input');
        
        // Add minimum length for password fields
        if (type === 'password') {
            input.setAttribute('minlength', '6');
        }
        
        container.appendChild(label);
        container.appendChild(input);
        
        return { container, input };
    }

    private createPasswordField(placeholder: string, id: string, label?: string): { container: HTMLDivElement; input: HTMLInputElement } {
        const container = document.createElement('div');
        container.classList.add('authiqa-labeled-input');
        
        // Create label if provided
        if (label) {
            const labelElement = document.createElement('label');
            labelElement.setAttribute('for', `authiqa-${id}`);
            labelElement.textContent = label;
            labelElement.classList.add('authiqa-label');
            container.appendChild(labelElement);
        }
        
        // Create password container
        const passwordContainer = document.createElement('div');
        passwordContainer.className = 'password-field-container';
        passwordContainer.classList.add('authiqa-password-container');
        
        // Create input
        const input = document.createElement('input');
        input.setAttribute('type', 'password');
        input.setAttribute('id', `authiqa-${id}`);
        input.setAttribute('name', id);
        input.setAttribute('placeholder', placeholder);
        input.setAttribute('required', 'true');
        input.setAttribute('minlength', '6');
        input.classList.add('authiqa-input');
        
        // Add input to password container
        passwordContainer.appendChild(input);
        
        // Create toggle button
        const toggleButton = document.createElement('button');
        toggleButton.setAttribute('type', 'button');
        toggleButton.classList.add('password-toggle');
        toggleButton.innerHTML = '👁️';
        
        toggleButton.addEventListener('click', () => {
            const type = input.getAttribute('type');
            input.setAttribute('type', type === 'password' ? 'text' : 'password');
            toggleButton.innerHTML = type === 'password' ? '👁️‍🗨️' : '👁️';
        });
        
        // Add toggle to password container
        passwordContainer.appendChild(toggleButton);
        
        // Create password validation container
        const validationContainer = document.createElement('div');
        validationContainer.classList.add('password-validation-container');
        
        // Create validation items
        const validations = [
            { id: 'length', text: '8+ Characters long', check: (val: string) => val.length >= 8 },
            { id: 'uppercase', text: '1+ Uppercase letter', check: (val: string) => /[A-Z]/.test(val) },
            { id: 'special', text: '1+ Special characters', check: (val: string) => /[!@#$%^&*(),.?":{}|<>]/.test(val) },
            { id: 'number', text: '1+ Number', check: (val: string) => /[0-9]/.test(val) }
        ];
        
        // Create validation elements
        validations.forEach(validation => {
            const validationItem = document.createElement('div');
            validationItem.classList.add('validation-item');
            validationItem.id = `validation-${validation.id}`;
            
            const validationDot = document.createElement('span');
            validationDot.classList.add('validation-dot');
            validationDot.textContent = '•';
            
            const validationText = document.createElement('span');
            validationText.classList.add('validation-text');
            validationText.textContent = validation.text;
            
            validationItem.appendChild(validationDot);
            validationItem.appendChild(validationText);
            validationContainer.appendChild(validationItem);
        });
        
        // Add validation container after password container
        container.appendChild(passwordContainer);
        container.appendChild(validationContainer);
        
        // Add input event listener for validation
        input.addEventListener('input', () => {
            const value = input.value;
            
            validations.forEach(validation => {
                const validationElement = document.getElementById(`validation-${validation.id}`);
                if (validationElement) {
                    if (validation.check(value)) {
                        validationElement.classList.add('valid');
                    } else {
                        validationElement.classList.remove('valid');
                    }
                }
            });
        });
        
        return { container, input };
    }

    private renderSignInForm(): void {
        const authiqaDiv = this.initializeContainer();
        authiqaDiv.innerHTML = '';

        const title = document.createElement('h1');
        title.classList.add('authiqa-title');
        title.textContent = this.config.customization?.typography?.titleText?.signinText || 'Sign in';
        authiqaDiv.appendChild(title);

        // Always render the form first
        const form = document.createElement('form');
        form.classList.add('authiqa-form');
        form.style.display = 'flex';
        form.style.flexDirection = 'column';
        form.style.gap = '1rem';

        // Create email field with label
        const { container: emailContainer, input: email } = this.createLabeledInput(
            'email',
            'email',
            this.config.customization?.inputs?.emailPlaceholder || 'Email Address',
            this.config.customization?.inputs?.emailLabel || 'Email'
        );

        // Create password field with label
        const { container: passwordContainer, input: password } = this.createPasswordField(
            this.config.customization?.inputs?.passwordPlaceholder || 'Password',
            'password',
            this.config.customization?.inputs?.passwordLabel || 'Password'
        );

        // --- Forgot Password Link ---
        const resetPath = this.config.resetAuthPath || this.authUrls?.reset || '#';
        const navLinks = this.config.customization?.navLinks;
        const forgotPrompt = navLinks?.forgotPrompt || 'Forgot Password?';
        const forgotLinkText = navLinks?.forgotLinkText || 'Reset';
        const forgotDiv = document.createElement('div');
        forgotDiv.className = 'forgot-password';
        forgotDiv.innerHTML = `${forgotPrompt} <a href="${resetPath}">${forgotLinkText}</a>`;

        // Submit button with custom text
        const submit = document.createElement('button');
        submit.setAttribute('type', 'submit');
        submit.classList.add('authiqa-button');
        submit.textContent = this.config.customization?.buttons?.signinText || 'Sign In';
        submit.style.marginTop = '0.5rem';

        form.appendChild(emailContainer);
        form.appendChild(passwordContainer);
        form.appendChild(forgotDiv);
        form.appendChild(submit);

        form.addEventListener('submit', async (event) => {
            event.preventDefault();
            submit.setAttribute('data-original-text', submit.textContent || 'Submit');
            this.setLoadingState(submit, true, 'signin');
            const formData = {
                email: email.value,
                password: password.value,
                parentPublicKey: this.config.publicKey,
            };
            try {
                const response = await fetch(`${this.api.getApiBase()}/auth/signin`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData),
                });
                const result: SigninResponse = await response.json();
                switch (response.status) {
                    case 200:
                        if (result.success && 'data' in result) {
                            if (result.data.passwordStatus?.expired) {
                                const resetUrl = this.config.resetAuthPath || this.authUrls?.reset || '';
                                this.showMessage(
                                    'Your password has expired. Please update it now.',
                                    'warning',
                                    resetUrl
                                );
                            } else if (result.data.passwordStatus?.daysUntilExpiry !== undefined && 
                                       result.data.passwordStatus.daysUntilExpiry <= 14) {
                                const days = result.data.passwordStatus.daysUntilExpiry;
                                const resetUrl = this.config.resetAuthPath || this.authUrls?.reset || '';
                                const message = `Your password will expire in ${days} day${days !== 1 ? 's' : ''}. Please update it soon.`;
                                if (days <= 3) {
                                    this.showMessage(message, 'warning');
                                    setTimeout(() => {
                                        const successUrl = this.config.successAuthPath || this.authUrls?.successful || '';
                                        window.location.href = successUrl;
                                    }, 3000);
                                } else {
                                    const successUrl = this.config.successAuthPath || this.authUrls?.successful || '';
                                    this.showMessage(message, 'warning', successUrl);
                                }
                            } else {
                                const redirectUrl = this.config.successAuthPath || this.authUrls?.successful || '';
                                this.showMessage(
                                    this.config.messages?.signinSuccess || 'Welcome back!',
                                    'success',
                                    redirectUrl
                                );
                            }
                        }
                        break;
                    default:
                        if (!result.success && 'error' in result) {
                            this.showMessage(result.error.message, 'error');
                        } else {
                            this.showMessage('An unexpected error occurred', 'error');
                        }
                }
            } catch (error) {
                console.error('Signin network error:', error);
                this.showMessage('Network error: Unable to connect to the server. Please check your connection and try again.', 'error');
            } finally {
                this.setLoadingState(submit, false, 'signin');
            }
        });

        // Insert the form into the container
        authiqaDiv.appendChild(form);

        // --- Google SSO Button (Dynamic Insert, styled, below main button) ---
        const maybeInsertGoogleButton = () => {
            if (
                this.googleSsoConfig?.enabled &&
                this.googleSsoConfig.clientId &&
                !document.getElementById('google-button-container')
            ) {
                if (!document.getElementById('google-identity-services')) {
                    const script = document.createElement('script');
                    script.src = 'https://accounts.google.com/gsi/client';
                    script.async = true;
                    script.defer = true;
                    script.id = 'google-identity-services';
                    document.head.appendChild(script);
                }
                // Create a simple container for Google button - no custom styling
                // --- One Tap Prompt: show on page load, only once per session ---
                if (typeof window._authiqaGoogleOneTapDismissed === 'undefined') {
                    window._authiqaGoogleOneTapDismissed = false;
                }
                
                // Show One Tap on page load (not on button click)
                if (!window._authiqaGoogleOneTapDismissed) {
                    const tryShowOneTap = () => {
                        if ((window as any).google && (window as any).google.accounts) {
                            (window as any).google.accounts.id.initialize({
                                client_id: this.googleSsoConfig!.clientId,
                                callback: async (response: any) => {
                                    const idToken = response.credential;
                                    if (!idToken) return;
                                    try {
                                        const res = await fetch(`${this.api.getApiBase()}/auth/google`, {
                                            method: 'POST',
                                            headers: { 'Content-Type': 'application/json' },
                                            body: JSON.stringify({
                                                idToken,
                                                parentPublicKey: this.config.publicKey
                                            })
                                        });
                                        const result = await res.json();
                                        if (res.status === 200 && result.success) {
                                            const redirectUrl = this.config.successAuthPath || this.authUrls?.successful || '/';
                                            window.location.href = redirectUrl;
                                        } else {
                                            this.showMessage(result.error?.message || 'Google sign-in failed', 'error');
                                        }
                                    } catch (err) {
                                        this.showMessage('Network error during Google sign-in', 'error');
                                    }
                                },
                                cancel_on_tap_outside: false,
                                auto_select: false
                            });
                            (window as any).google.accounts.id.prompt();
                            window._authiqaGoogleOneTapDismissed = true;
                        } else {
                            setTimeout(tryShowOneTap, 100);
                        }
                    };
                    tryShowOneTap();
                }
                
                // --- Create a separate container for Google button ---
                const googleButtonContainer = document.createElement('div');
                googleButtonContainer.id = 'google-button-container';
                googleButtonContainer.style.margin = '0.5rem 0 0 0';
                
                // --- Use renderButton instead of prompt for popup ---
                const initializeGoogleButton = () => {
                    if ((window as any).google && (window as any).google.accounts) {
                        (window as any).google.accounts.id.initialize({
                            client_id: this.googleSsoConfig!.clientId,
                            callback: async (response: any) => {
                                const idToken = response.credential;
                                if (!idToken) return;
                                try {
                                    const res = await fetch(`${this.api.getApiBase()}/auth/google`, {
                                        method: 'POST',
                                        headers: { 'Content-Type': 'application/json' },
                                        body: JSON.stringify({
                                            idToken,
                                            parentPublicKey: this.config.publicKey
                                        })
                                    });
                                    const result = await res.json();
                                    if (res.status === 200 && result.success) {
                                        const redirectUrl = this.config.successAuthPath || this.authUrls?.successful || '/';
                                        window.location.href = redirectUrl;
                                    } else {
                                        this.showMessage(result.error?.message || 'Google sign-in failed', 'error');
                                    }
                                } catch (err) {
                                    this.showMessage('Network error during Google sign-in', 'error');
                                }
                            },
                            ux_mode: 'popup'
                        });
                        
                        // Use renderButton with full width style
                        (window as any).google.accounts.id.renderButton(
                            googleButtonContainer,
                            {
                                theme: 'outline',
                                size: 'large',
                                text: 'continue_with',
                                shape: 'rectangular',
                                logo_alignment: 'left',
                                width: '100%'
                            }
                        );
                    } else {
                        setTimeout(initializeGoogleButton, 100);
                    }
                };

                // Initialize Google button
                initializeGoogleButton();
                // Insert Google button container below the main submit button
                form.insertBefore(googleButtonContainer, submit.nextSibling);
            }
        };
        maybeInsertGoogleButton();
        if (!this.googleSsoConfig?.enabled || !this.googleSsoConfig.clientId) {
            const interval = setInterval(() => {
                if (this.googleSsoConfig?.enabled && this.googleSsoConfig.clientId && !document.getElementById('google-button-container')) {
                    maybeInsertGoogleButton();
                    clearInterval(interval);
                }
            }, 200);
            setTimeout(() => clearInterval(interval), 5000);
        }
        // --- End Google SSO Button ---

        // --- Sign Up Navigation Link ---
        const signupPath = this.authUrls?.signup || '#';
        const signupPrompt = navLinks?.signupPrompt || "Don't have an account?";
        const signupLinkText = navLinks?.signupLinkText || 'Sign Up';
        const navDiv = document.createElement('div');
        navDiv.className = 'alternate-action';
        navDiv.innerHTML = `${signupPrompt} <a href="${signupPath}">${signupLinkText}</a>`;
        form.appendChild(navDiv);
    }

    private renderSignUpForm(): void {
        const authiqaDiv = this.initializeContainer();
        // Clear any existing content
        authiqaDiv.innerHTML = '';

        const navLinks = this.config.customization?.navLinks;
        const title = document.createElement('h1');
        title.classList.add('authiqa-title'); // Add class
        // Apply custom title text if available
        title.textContent = this.config.customization?.typography?.titleText?.signupText || 'Sign up';
        authiqaDiv.appendChild(title);

        // Always render the form first
        const form = document.createElement('form');
        form.classList.add('authiqa-form'); // Add class
        form.style.display = 'flex';
        form.style.flexDirection = 'column';
        form.style.gap = '1rem'; /* Set gap to 1rem (about 16px) */

        // Username field with label
        const { container: usernameContainer, input: username } = this.createLabeledInput(
            'text',
            'username',
            this.config.customization?.inputs?.usernamePlaceholder || 'Username',
            this.config.customization?.inputs?.usernameLabel || 'Username'
        );

        // Email field with label
        const { container: emailContainer, input: email } = this.createLabeledInput(
            'email',
            'email',
            this.config.customization?.inputs?.emailPlaceholder || 'Email Address',
            this.config.customization?.inputs?.emailLabel || 'Email'
        );

        // Password field with label
        const { container: passwordContainer, input: password } = this.createPasswordField(
            this.config.customization?.inputs?.passwordPlaceholder || 'Password',
            'password',
            this.config.customization?.inputs?.passwordLabel || 'Password'
        );
        
        // Add to form
        form.appendChild(usernameContainer);
        form.appendChild(emailContainer);
        form.appendChild(passwordContainer);

        // Create terms checkbox container
        const termsContainer = document.createElement('div');
        termsContainer.classList.add('terms-container');
        termsContainer.style.display = 'flex';
        termsContainer.style.alignItems = 'flex-start';
        termsContainer.style.marginBottom = '1rem'; // Consistent margin

        const termsCheckbox = document.createElement('input');
        termsCheckbox.setAttribute('type', 'checkbox');
        termsCheckbox.setAttribute('id', 'terms');
        termsCheckbox.setAttribute('name', 'terms');
        termsCheckbox.setAttribute('required', 'required');
        termsCheckbox.style.marginTop = '0.25rem'; // Align with text
        termsCheckbox.style.marginRight = '0.5rem'; // Consistent margin

        const termsLabel = document.createElement('label');
        termsLabel.setAttribute('for', 'terms');
        termsLabel.style.flex = '1';
        termsLabel.style.margin = '0';
        termsLabel.style.padding = '0';
        termsLabel.style.color = '#525252';
        termsLabel.style.fontSize = '0.875rem';
        termsLabel.style.lineHeight = '1.4';

        const { agreePrefix, andConnector, defaultPrefix, linkText } = 
            this.config.customization?.typography?.termsText || {
                agreePrefix: 'I agree with the',
                andConnector: 'and',
                defaultPrefix: 'default',
                linkText: {
                    terms: 'Terms of Service',
                    privacy: 'Privacy Policy',
                    notifications: 'Notification Settings'
                }
            };

        termsLabel.innerHTML = `${agreePrefix} <a href="${this.config.termsAndConditions || '#'}">${linkText.terms}</a> <a href="${this.config.privacy || '#'}">${linkText.privacy}</a> ${andConnector} ${defaultPrefix} <a href="${this.config.notificationSettings || '#'}">${linkText.notifications}</a>.`;

        termsContainer.appendChild(termsCheckbox);
        termsContainer.appendChild(termsLabel);

        // Submit button with custom text
        const submit = document.createElement('button');
        submit.setAttribute('type', 'submit');
        submit.classList.add('authiqa-button'); // Add class
        const buttonText = this.config.customization?.buttons?.signupText || 'Create Account';
        submit.textContent = buttonText;

        form.appendChild(termsContainer);
        form.appendChild(submit);

        form.addEventListener('submit', async (event) => {
            event.preventDefault();

            if (!termsCheckbox.checked) {
                this.showMessage('Please accept the terms and conditions', 'error');
                return;
            }

            // Add password validation before submission
            const passwordValidation = this.validatePassword(password.value);
            if (!passwordValidation.isValid && passwordValidation.error) {
                this.showMessage(`${passwordValidation.error.message} (${passwordValidation.error.code})`, 'error');
                return;
            }

            submit.setAttribute('data-original-text', submit.textContent || 'Submit');
            this.setLoadingState(submit, true, 'signup');

            const formData = {
                username: username.value,
                email: email.value,
                password: password.value,
                parentPublicKey: this.config.publicKey, // Changed from parentApiKey
                verifyAuthPath: this.config.verifyAuthPath
            };


            try {
                const response = await fetch(`${this.api.getApiBase()}/auth/signup`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData),
                });

                const result = await response.json();
                
          
                switch (response.status) {
                    case 200:
                        if (result.success && result.data) {
                            const redirectUrl = this.config.resendAuthPath || this.authUrls?.resend;
                            this.showMessage(
                                'Successfully signed up! Please check your email for verification link', 
                                'success', 
                                `${redirectUrl}?email=${encodeURIComponent(email.value)}`
                            );
                        }
                        break;

                    case 409:
                        // Handle conflict errors (email/username already exists)
                        const conflictError = result.error;
                        switch (conflictError.code) {
                            case 'EMAIL_ALREADY_EXISTS':
                                this.showMessage(`${conflictError.message} (${conflictError.code})`, 'error');
                                break;
                            case 'USERNAME_ALREADY_EXISTS':
                                this.showMessage(`${conflictError.message} (${conflictError.code})`, 'error');
                            break;
                            case 'DUPLICATE_EMAIL_USERNAME_COMBO':
                                this.showMessage(`${conflictError.message} (${conflictError.code})`, 'error');
                                break;
                            default:
                                this.showMessage(`${conflictError.message}`, 'error');
                        }
                        break;

                    case 400:
                        // Handle validation errors
                        const validationError = result.error;
                        switch (validationError.code) {
                            case 'MISSING_REQUEST_BODY':
                            case 'MISSING_REQUIRED_FIELDS':
                            case 'INVALID_EMAIL_FORMAT':
                            case 'INVALID_PASSWORD_FORMAT':
                            case 'INVALID_USERNAME_FORMAT':
                            case 'MISSING_PARENT_PUBLIC_KEY':
                                this.showMessage(`${validationError.message} (${validationError.code})`, 'error');
                            break;
                            default:
                                this.showMessage(`${validationError.message}`, 'error');
                        }
                            break;

                        case 401:
                            // Handle unauthorized errors
                            const unauthorizedError = result.error;
                            if (unauthorizedError.code === 'INVALID_PARENT_PUBLIC_KEY') {
                                this.showMessage(`${unauthorizedError.message} (${unauthorizedError.code})`, 'error');
                            } else {
                                this.showMessage(`${unauthorizedError.message}`, 'error');
                            }
                            break;

                        case 403:
                            // Handle forbidden errors
                            const forbiddenError = result.error;
                            if (forbiddenError.code === 'PARENT_ACCOUNT_INACTIVE') {
                                this.showMessage(`${forbiddenError.message} (${forbiddenError.code})`, 'error');
                            } else {
                                this.showMessage(`${forbiddenError.message}`, 'error');
                            }
                            break;

                        case 500:
                            this.showMessage('An internal server error occurred. Please try again later.', 'error');
                            break;

                        default:
                            this.showMessage('An unexpected error occurred. Please try again.', 'error');
                    }
                } catch (error) {
                    console.error('Signup network error:', error);
                    this.showMessage('Network error: Unable to connect to the server. Please check your connection and try again.', 'error');
                } finally {
                    this.setLoadingState(submit, false, 'signup');
                }
            });

            // Insert the form into the container
            authiqaDiv.appendChild(form);

            // --- Google SSO Button (Dynamic Insert, styled, below main button) ---
            const maybeInsertGoogleButton = () => {
                if (
                    this.googleSsoConfig?.enabled &&
                    this.googleSsoConfig.clientId &&
                    !document.getElementById('google-signup-button-container')
                ) {
                    if (!document.getElementById('google-identity-services')) {
                        const script = document.createElement('script');
                        script.src = 'https://accounts.google.com/gsi/client';
                        script.async = true;
                        script.defer = true;
                        script.id = 'google-identity-services';
                        document.head.appendChild(script);
                    }
                    // Create a simple container for Google button - no custom styling
                    // --- One Tap Prompt: show on page load, only once per session ---
                    if (typeof window._authiqaGoogleOneTapDismissed === 'undefined') {
                        window._authiqaGoogleOneTapDismissed = false;
                    }
                    
                    // Show One Tap on page load (not on button click)
                    if (!window._authiqaGoogleOneTapDismissed) {
                        const tryShowOneTap = () => {
                            if ((window as any).google && (window as any).google.accounts) {
                                (window as any).google.accounts.id.initialize({
                                    client_id: this.googleSsoConfig!.clientId,
                                    callback: async (response: any) => {
                                        const idToken = response.credential;
                                        if (!idToken) return;
                                        try {
                                            const res = await fetch(`${this.api.getApiBase()}/auth/google`, {
                                                method: 'POST',
                                                headers: { 'Content-Type': 'application/json' },
                                                body: JSON.stringify({
                                                    idToken,
                                                    parentPublicKey: this.config.publicKey
                                                })
                                            });
                                            const result = await res.json();
                                            if (res.status === 200 && result.success) {
                                                const redirectUrl = this.config.successAuthPath || this.authUrls?.successful || '/';
                                                window.location.href = redirectUrl;
                                            } else {
                                                this.showMessage(result.error?.message || 'Google sign-up failed', 'error');
                                            }
                                        } catch (err) {
                                            this.showMessage('Network error during Google sign-up', 'error');
                                        }
                                    },
                                    cancel_on_tap_outside: false,
                                    auto_select: false
                                });
                                (window as any).google.accounts.id.prompt();
                                window._authiqaGoogleOneTapDismissed = true;
                            } else {
                                setTimeout(tryShowOneTap, 100);
                            }
                        };
                        tryShowOneTap();
                    }
                    
                    // --- Create a separate container for Google button ---
                    const googleButtonContainer = document.createElement('div');
                    googleButtonContainer.id = 'google-signup-button-container';
                    googleButtonContainer.style.margin = '0.5rem 0 0 0';
                    
                    // --- Use renderButton instead of prompt for popup ---
                    const initializeGoogleButton = () => {
                        if ((window as any).google && (window as any).google.accounts) {
                            (window as any).google.accounts.id.initialize({
                                client_id: this.googleSsoConfig!.clientId,
                                callback: async (response: any) => {
                                    const idToken = response.credential;
                                    if (!idToken) return;
                                    try {
                                        const res = await fetch(`${this.api.getApiBase()}/auth/google`, {
                                            method: 'POST',
                                            headers: { 'Content-Type': 'application/json' },
                                            body: JSON.stringify({
                                                idToken,
                                                parentPublicKey: this.config.publicKey
                                            })
                                        });
                                        const result = await res.json();
                                        if (res.status === 200 && result.success) {
                                            const redirectUrl = this.config.successAuthPath || this.authUrls?.successful || '/';
                                            window.location.href = redirectUrl;
                                        } else {
                                            this.showMessage(result.error?.message || 'Google sign-up failed', 'error');
                                        }
                                    } catch (err) {
                                        this.showMessage('Network error during Google sign-up', 'error');
                                    }
                                },
                                ux_mode: 'popup'
                            });
                            
                            // Use renderButton with full width style
                            (window as any).google.accounts.id.renderButton(
                                googleButtonContainer,
                                {
                                    theme: 'outline',
                                    size: 'large',
                                    text: 'continue_with',
                                    shape: 'rectangular',
                                    logo_alignment: 'left',
                                    width: '100%'
                                }
                            );
                        } else {
                            setTimeout(initializeGoogleButton, 100);
                        }
                    };

                    // Initialize Google button
                    initializeGoogleButton();
                    // Insert Google button container below the main submit button
                    form.insertBefore(googleButtonContainer, submit.nextSibling);
                }
            };
            maybeInsertGoogleButton();
            if (!this.googleSsoConfig?.enabled || !this.googleSsoConfig.clientId) {
                const interval = setInterval(() => {
                    if (this.googleSsoConfig?.enabled && this.googleSsoConfig.clientId && !document.getElementById('google-signup-button-container')) {
                        maybeInsertGoogleButton();
                        clearInterval(interval);
                    }
                }, 200);
                setTimeout(() => clearInterval(interval), 5000);
            }
            // --- End Google SSO Button ---

            // --- Sign In Navigation Link ---
            const signinPath = this.config.signinAuthPath || this.authUrls?.signin || '#';
            const signinPrompt = navLinks?.signinPrompt || 'Already have an account?';
            const signinLinkText = navLinks?.signinLinkText || 'Sign In';
            const navDiv = document.createElement('div');
            navDiv.className = 'alternate-action';
            navDiv.innerHTML = `${signinPrompt} <a href="${signinPath}">${signinLinkText}</a>`;
            form.appendChild(navDiv);
        }

    private validatePassword(password: string): { isValid: boolean; error?: { code: string; message: string } } {
        // Password must contain:
        // 1. At least 8 characters
        // 2. At least one uppercase letter
        // 3. At least one number
        // 4. At least one special character
        const hasMinLength = password.length >= 8;
        const hasUpperCase = /[A-Z]/.test(password);
        const hasNumber = /[0-9]/.test(password);
        const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);

        if (!hasMinLength || !hasUpperCase || !hasNumber || !hasSpecialChar) {
            let message = 'Password must contain:';
            if (!hasMinLength) message += ' at least 8 characters,';
            if (!hasUpperCase) message += ' at least one uppercase letter,';
            if (!hasNumber) message += ' at least one number,';
            if (!hasSpecialChar) message += ' at least one special character,';
            
            // Remove trailing comma and add period
            message = message.replace(/,$/, '');
            
            return {
                isValid: false,
                error: {
                    code: 'INVALID_PASSWORD_FORMAT',
                    message: message
                }
            };
        }

        return { isValid: true };
    }

    private renderResetPasswordForm(): void {
        const authiqaDiv = this.initializeContainer();
        authiqaDiv.innerHTML = '';

        const title = document.createElement('h1');
        title.classList.add('authiqa-title'); // Add class
        // Apply custom title text
        title.textContent = this.config.customization?.typography?.titleText?.resetText || 'Reset Password';
        
        // Add subtitle text if available
        if (this.config.customization?.typography?.subtitleText?.resetText) {
            title.setAttribute('data-subtitle', this.config.customization.typography.subtitleText.resetText);
        }
        
        authiqaDiv.appendChild(title);

        const form = document.createElement('form');
        form.classList.add('authiqa-form'); // Add class
        form.style.display = 'flex';
        form.style.flexDirection = 'column';
        form.style.gap = '1rem'; /* Set gap to 1rem (about 16px) */

        // Email field with label
        const { container: emailContainer, input: email } = this.createLabeledInput(
            'email',
            'email',
            this.config.customization?.inputs?.emailPlaceholder || 'Email Address',
            this.config.customization?.inputs?.emailLabel || 'Email'
        );
        
        // Add to form
        form.appendChild(emailContainer);
        
        // Submit button with custom text
        const submit = document.createElement('button');
        submit.setAttribute('type', 'submit');
        submit.classList.add('authiqa-button'); // Add class
        submit.textContent = this.config.customization?.buttons?.resetText || 'Reset Password';
        
        form.appendChild(submit);

        // --- Navigation Link to Sign In (alternate-action) ---
        const signinPath = this.config.signinAuthPath || this.authUrls?.signin || '#';
        const navLinks = this.config.customization?.navLinks;
        const backToSigninPrompt = navLinks?.backToSigninPrompt || 'Back to Sign In?';
        const navDiv = document.createElement('div');
        navDiv.className = 'alternate-action';
        navDiv.innerHTML = `${backToSigninPrompt} <a href="${signinPath}">Sign In</a>`;
        form.appendChild(navDiv);

        form.addEventListener('submit', async (event) => {
            event.preventDefault();

            // Add loading state
            this.setLoadingState(submit, true, 'reset');

            const formData = {
                email: email.value,
                parentPublicKey: this.config.publicKey, // Changed from parentApiKey
                updatePasswordPath: this.config.updatePasswordPath
            };

            try {
                const response = await fetch(`${this.api.getApiBase()}/auth/reset-password`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData),
                });

                const result: ApiResponse<{ message: string }> = await response.json();

                switch (response.status) {
                    case 200:
                        if (result.success && result.data) {
                            this.showMessage(
                                this.config.messages?.resetSuccess || result.data.message,
                                'success'
                            );
                        }
                        break;

                    default:
                        if (!result.success && result.error) {
                            this.showMessage(result.error.message, 'error');
                        } else {
                            this.showMessage('An unexpected error occurred', 'error');
                        }
                }
            } catch (error) {
                console.error('Reset password network error:', error);
                this.showMessage('Unable to connect to the server', 'error');
            } finally {
                // Reset loading state
                this.setLoadingState(submit, false, 'reset');
            }
        });

        authiqaDiv.appendChild(form);
    }

    private renderUpdatePasswordForm(): void {
        const authiqaDiv = this.initializeContainer();
        authiqaDiv.innerHTML = '';

        const title = document.createElement('h1');
        title.classList.add('authiqa-title'); // Add class
        // Apply custom title text
        title.textContent = this.config.customization?.typography?.titleText?.updateText || 'Update Password';
        authiqaDiv.appendChild(title);

        // Get token from URL first
        const urlParams = new URLSearchParams(window.location.search);
        const tokenFromUrl = urlParams.get('token');
        
        const form = document.createElement('form');
        form.classList.add('authiqa-form'); // Add class
        form.style.display = 'flex';
        form.style.flexDirection = 'column';
        form.style.gap = '1rem'; /* Set gap to 1rem (about 16px) */

        // Create hidden token input if URL token exists
        if (tokenFromUrl) {
            const hiddenToken = document.createElement('input');
            hiddenToken.setAttribute('type', 'hidden');
            hiddenToken.setAttribute('name', 'token');
            hiddenToken.value = tokenFromUrl;
            form.appendChild(hiddenToken);
        } else {
            console.warn('No token found in URL - password reset may fail');
        }

        // Create new password field WITHOUT validation indicators
        const newPasswordContainer = document.createElement('div');
        newPasswordContainer.classList.add('authiqa-labeled-input');
        
        // Create label
        const newPasswordLabel = document.createElement('label');
        newPasswordLabel.setAttribute('for', 'authiqa-newPassword');
        newPasswordLabel.textContent = this.config.customization?.inputs?.passwordLabel || 'New Password';
        newPasswordLabel.classList.add('authiqa-label');
        newPasswordContainer.appendChild(newPasswordLabel);
        
        // Create password container
        const passwordFieldContainer = document.createElement('div');
        passwordFieldContainer.className = 'password-field-container';
        passwordFieldContainer.classList.add('authiqa-password-container');
        
        // Create input
        const newPassword = document.createElement('input');
        newPassword.setAttribute('type', 'password');
        newPassword.setAttribute('id', 'authiqa-newPassword');
        newPassword.setAttribute('name', 'newPassword');
        newPassword.setAttribute('placeholder', this.config.customization?.inputs?.passwordPlaceholder || 'New Password');
        newPassword.setAttribute('required', 'true');
        newPassword.setAttribute('minlength', '6');
        newPassword.classList.add('authiqa-input');
        
        // Add input to password container
        passwordFieldContainer.appendChild(newPassword);
        
        // Create toggle button
        const toggleButton = document.createElement('button');
        toggleButton.setAttribute('type', 'button');
        toggleButton.classList.add('password-toggle');
        toggleButton.innerHTML = '👁️';
        
        toggleButton.addEventListener('click', () => {
            const type = newPassword.getAttribute('type');
            newPassword.setAttribute('type', type === 'password' ? 'text' : 'password');
            toggleButton.innerHTML = type === 'password' ? '👁️‍🗨️' : '👁️';
        });
        
        // Add toggle to password container
        passwordFieldContainer.appendChild(toggleButton);
        newPasswordContainer.appendChild(passwordFieldContainer);
        
        // Confirm password field with label
        const confirmPasswordContainer = document.createElement('div');
        confirmPasswordContainer.classList.add('authiqa-labeled-input');
        
        // Create label
        const confirmPasswordLabel = document.createElement('label');
        confirmPasswordLabel.setAttribute('for', 'authiqa-confirmPassword');
        confirmPasswordLabel.textContent = this.config.customization?.inputs?.confirmPasswordLabel || 'Confirm Password';
        confirmPasswordLabel.classList.add('authiqa-label');
        confirmPasswordContainer.appendChild(confirmPasswordLabel);
        
        // Create password container
        const confirmPasswordFieldContainer = document.createElement('div');
        confirmPasswordFieldContainer.className = 'password-field-container';
        confirmPasswordFieldContainer.classList.add('authiqa-password-container');
        
        // Create input
        const confirmPassword = document.createElement('input');
        confirmPassword.setAttribute('type', 'password');
        confirmPassword.setAttribute('id', 'authiqa-confirmPassword');
        confirmPassword.setAttribute('name', 'confirmPassword');
        confirmPassword.setAttribute('placeholder', this.config.customization?.inputs?.confirmPasswordPlaceholder || 'Confirm Password');
        confirmPassword.setAttribute('required', 'true');
        confirmPassword.classList.add('authiqa-input');
        
        // Add input to password container
        confirmPasswordFieldContainer.appendChild(confirmPassword);
        
        // Create toggle button for confirm password
        const confirmToggleButton = document.createElement('button');
        confirmToggleButton.setAttribute('type', 'button');
        confirmToggleButton.classList.add('password-toggle');
        confirmToggleButton.innerHTML = '👁️';
        
        confirmToggleButton.addEventListener('click', () => {
            const type = confirmPassword.getAttribute('type');
            confirmPassword.setAttribute('type', type === 'password' ? 'text' : 'password');
            confirmToggleButton.innerHTML = type === 'password' ? '👁️‍🗨️' : '👁️';
        });
        
        // Add toggle to password container
        confirmPasswordFieldContainer.appendChild(confirmToggleButton);
        confirmPasswordContainer.appendChild(confirmPasswordFieldContainer);
        
        // Create validation container
        const validationContainer = document.createElement('div');
        validationContainer.classList.add('password-validation-container');
        
        // Create validation items
        const validations = [
            { id: 'length', text: '8+ Characters long', check: (val: string) => val.length >= 8 },
            { id: 'uppercase', text: '1+ Uppercase letter', check: (val: string) => /[A-Z]/.test(val) },
            { id: 'special', text: '1+ Special characters', check: (val: string) => /[!@#$%^&*(),.?":{}|<>]/.test(val) },
            { id: 'number', text: '1+ Number', check: (val: string) => /[0-9]/.test(val) }
        ];
        
        // Create validation elements
        validations.forEach(validation => {
            const validationItem = document.createElement('div');
            validationItem.classList.add('validation-item');
            validationItem.id = `validation-${validation.id}`;
            
            const validationDot = document.createElement('span');
            validationDot.classList.add('validation-dot');
            validationDot.textContent = '•';
            
            const validationText = document.createElement('span');
            validationText.classList.add('validation-text');
            validationText.textContent = validation.text;
            
            validationItem.appendChild(validationDot);
            validationItem.appendChild(validationText);
            validationContainer.appendChild(validationItem);
        });
        
        // Add to form
        form.appendChild(newPasswordContainer);
        form.appendChild(confirmPasswordContainer);
        form.appendChild(validationContainer); // Add validation container after both password fields
        
        // Add input event listener for validation
        newPassword.addEventListener('input', () => {
            const value = newPassword.value;
            
            validations.forEach(validation => {
                const validationElement = document.getElementById(`validation-${validation.id}`);
                if (validationElement) {
                    if (validation.check(value)) {
                        validationElement.classList.add('valid');
                    } else {
                        validationElement.classList.remove('valid');
                    }
                }
            });
        });
        
        // Add real-time password matching validation
        const validatePasswords = () => {
            if (confirmPassword.value) {
                if (newPassword.value !== confirmPassword.value) {
                    confirmPassword.setCustomValidity('Passwords do not match');
                } else {
                    confirmPassword.setCustomValidity('');
                }
            }
        };
        
        newPassword.addEventListener('input', validatePasswords);
        confirmPassword.addEventListener('input', validatePasswords);

        // Submit button with custom text
        const submit = document.createElement('button');
        submit.setAttribute('type', 'submit');
        submit.classList.add('authiqa-button'); // Add class
        submit.textContent = this.config.customization?.buttons?.updateText || 'Update Password';

        form.appendChild(submit);

        form.addEventListener('submit', async (event) => {
            event.preventDefault();

            // Frontend validation
            if (newPassword.value !== confirmPassword.value) {
                this.showMessage('Passwords do not match', 'error');
                return;
            }

            this.setLoadingState(submit, true, 'update');

            const formData = {
                token: tokenFromUrl,
                password: newPassword.value
            };

            try {
                const response = await fetch(`${this.api.getApiBase()}/auth/update-password`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData),
                });

                const result = await response.json();

                switch (response.status) {
                    case 200:
                        if (result.success && result.data) {
                            const redirectUrl = this.config.signinAuthPath || this.authUrls?.signin;
                            this.showMessage(
                                this.config.messages?.updateSuccess || 'Password updated successfully!',
                                'success',
                                redirectUrl
                            );
                        }
                        break;

                    default:
                        // Show original error message from server
                        if (!result.success && result.error) {
                            this.showMessage(result.error.message, 'error');
                        } else {
                            this.showMessage('An unexpected error occurred', 'error');
                        }
                }
            } catch (error) {
                this.showMessage('Network error: Unable to connect to the server', 'error');
            } finally {
                this.setLoadingState(submit, false, 'update');
            }
        });

        authiqaDiv.appendChild(form);
    }

    private renderResendConfirmationForm(): void {
        const authiqaDiv = this.initializeContainer();
        authiqaDiv.innerHTML = '';

        const title = document.createElement('h1');
        title.classList.add('authiqa-title'); // Add class
        // Apply custom title text
        title.textContent = this.config.customization?.typography?.titleText?.resendText || 'Resend Confirmation';
        authiqaDiv.appendChild(title);

        const form = document.createElement('form');
        form.classList.add('authiqa-form'); // Add class
        form.style.display = 'flex';
        form.style.flexDirection = 'column';
        form.style.gap = '1rem'; /* Set gap to 1rem (about 16px) */

        // Get email from URL if available
        const urlParams = new URLSearchParams(window.location.search);
        const emailFromUrl = urlParams.get('email');

        // Create email field with label
        const { container: emailContainer, input: email } = this.createLabeledInput(
            'email',
            'email',
            this.config.customization?.inputs?.emailPlaceholder || 'Email Address',
            this.config.customization?.inputs?.emailLabel || 'Email'
        );
        
        // Set email value if available from URL
        if (emailFromUrl) {
            email.value = emailFromUrl;
        }

        // Submit button with custom text
        const submit = document.createElement('button');
        submit.setAttribute('type', 'submit');
        submit.classList.add('authiqa-button'); // Add class
        submit.textContent = this.config.customization?.buttons?.resendText || 'Resend Confirmation';
        
        form.appendChild(emailContainer);
        form.appendChild(submit);

        // --- Navigation Link to Sign In (alternate-action) ---
        const signinPath = this.config.signinAuthPath || this.authUrls?.signin || '#';
        const navLinks = this.config.customization?.navLinks;
        const backToSigninPrompt = navLinks?.backToSigninPrompt || 'Back to Sign In?';
        const navDiv = document.createElement('div');
        navDiv.className = 'alternate-action';
        navDiv.innerHTML = `${backToSigninPrompt} <a href="${signinPath}">Sign In</a>`;
        form.appendChild(navDiv);

        form.addEventListener('submit', async (event) => {
            event.preventDefault();
            this.setLoadingState(submit, true, 'resend');

            const formData = {
                email: email.value,
                parentPublicKey: this.config.publicKey, // Changed from parentApiKey
                verifyAuthPath: this.config.verifyAuthPath
            };

            try {
                const response = await fetch(`${this.api.getApiBase()}/auth/request-new-confirmation`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData),
                });

                const result = await response.json();

                switch (response.status) {
                    case 200:
                        if (result.success && result.data) {
                            this.showMessage(
                                this.config.messages?.resendSuccess || result.data.message,
                                'success'
                            );
                        }
                        break;

                    default:
                        // Show original error message from server
                        if (!result.success && result.error) {
                            this.showMessage(result.error.message, 'error');
                        } else {
                            this.showMessage('An unexpected error occurred', 'error');
                        }
                }
            } catch (error) {
                this.showMessage('Network error: Unable to connect to the server', 'error');
            } finally {
                this.setLoadingState(submit, false, 'resend');
            }
        });

        authiqaDiv.appendChild(form);
    }

    private renderVerificationStatus(status: 'loading' | 'success' | 'error', message: string): void {
        const authiqaDiv = this.initializeContainer();
        authiqaDiv.innerHTML = '';

        const container = document.createElement('div');
        container.className = 'verification-status';

        const title = document.createElement('h1');
        title.textContent = 'Email Verification';

        const statusContainer = document.createElement('div');
        
        if (status === 'loading') {
            const loader = document.createElement('div');
            loader.className = 'verification-loader';
            statusContainer.appendChild(loader);
            message = this.config.messages?.verificationLoading || message;
        } else {
            const icon = document.createElement('div');
            icon.className = `verification-icon ${status}`;
            icon.innerHTML = status === 'success' ? '✓' : '✕';
            statusContainer.appendChild(icon);
            
            // Only use custom message for success, not for error
            if (status === 'success') {
                message = this.config.messages?.verificationSuccess || message;
            }
            // For error, use the original message
        }

        const messageElement = document.createElement('p');
        messageElement.textContent = message;

        container.appendChild(title);
        container.appendChild(statusContainer);
        container.appendChild(messageElement);
        authiqaDiv.appendChild(container);
    }

    private async handleEmailVerification(): Promise<void> {
        const urlParams = new URLSearchParams(window.location.search);
        const token = urlParams.get('token');
        
        if (!token) {
            this.renderVerificationStatus('error', 'Invalid verification token (INVALID_TOKEN)');
            return;
        }

        // Show loading state
        this.renderVerificationStatus('loading', this.config.messages?.verificationLoading || 'Verifying your email address...');

        try {
            const response = await fetch(`${this.api.getApiBase()}/auth/confirm-email?token=${encodeURIComponent(token)}`, {
                headers: {
                    'X-Public-Key': this.config.publicKey,
                    'Content-Type': 'application/json'
                }
            });

            const result = await response.json();

            switch (response.status) {
                case 200:
                    if (result.success && result.data) {
                        this.renderVerificationStatus('success', 
                            this.config.messages?.verificationSuccess || result.data.message || 'Email verified successfully'
                        );
                        // Use custom signin path if provided, otherwise use default
                        const redirectUrl = this.config.signinAuthPath || this.authUrls?.signin;
                        setTimeout(() => {
                            window.location.href = redirectUrl || '/';
                        }, 2000);
                    }
                    break;

                case 400:
                    const badRequestError = result.error;
                    this.renderVerificationStatus('error', `${badRequestError.message} (${badRequestError.code})`);
                    break;

                case 404:
                    const notFoundError = result.error;
                    this.renderVerificationStatus('error', `${notFoundError.message} (${notFoundError.code})`);
                    break;

                case 500:
                    const serverError = result.error;
                    this.renderVerificationStatus('error', `${serverError.message} (${serverError.code})`);
                    break;

                default:
                    this.renderVerificationStatus('error', 'An unexpected error occurred. Please try again.');
            }
        } catch (error) {
            console.error('Error during email verification:', error);
            this.renderVerificationStatus('error', 
                'Network error: Unable to connect to the server. Please check your connection and try again.'
            );
        }
    }

    private showMessage(displayMessage: string, type: 'success' | 'error' | 'warning', redirect?: string): void {
        const messageElement = document.createElement('div');
        messageElement.classList.add('authiqa-message');
        messageElement.classList.add(`authiqa-message-${type}`);
        
        // Set different colors based on message type
        if (type === 'success') {
            messageElement.style.backgroundColor = '#4caf50';
        } else if (type === 'error') {
            messageElement.style.backgroundColor = '#f44336';
        } else if (type === 'warning') {
            messageElement.style.backgroundColor = '#ff9800';
        }
        
        // Handle redirect logic - only if no specific redirect URL was provided
        let redirectUrl = redirect;
        if (type === 'success' && !redirect) {  // Only provide fallback if no redirect was specified
            switch (this.currentAction) {
                case 'signin':
                    redirectUrl = this.config.successAuthPath || this.authUrls?.successful;
                    break;
                case 'update':
                    redirectUrl = this.config.signinAuthPath || this.authUrls?.signin;
                    break;
                case 'signup':
                    redirectUrl = this.config.resendAuthPath || this.authUrls?.resend;
                    break;
            }
        }

        const existingMessage = document.querySelector('.authiqa-message');
        if (existingMessage) {
            existingMessage.remove();
        }

        messageElement.textContent = displayMessage;

        document.body.appendChild(messageElement);
        messageElement.classList.add('show');

        // Debug log for message

        // Increase durations: error 7000ms, success 4000ms, warning 5000ms
        let duration = 2000;
        if (type === 'error') duration = 7000;
        else if (type === 'success') duration = 4000;
        else if (type === 'warning') duration = 5000;

        setTimeout(() => {
            messageElement.classList.remove('show');
            setTimeout(() => {
                messageElement.remove();
                if (redirectUrl) {
                    window.location.href = redirectUrl;
                }
            }, 300);
        }, duration);
    }

    private getCustomSuccessMessage(defaultMessage: string): string {
        const action = this.currentAction; // Need to add this property to track current form
        switch(action) {
            case 'signin':
                return this.config.messages?.signinSuccess || defaultMessage;
            case 'signup':
                return this.config.messages?.signupSuccess || defaultMessage;
            case 'reset':
                return this.config.messages?.resetSuccess || defaultMessage;
            case 'update':
                return this.config.messages?.updateSuccess || defaultMessage;
            case 'resend':
                return this.config.messages?.resendSuccess || defaultMessage;
            default:
                return defaultMessage;
        }
    }

    private setLoadingState(button: HTMLButtonElement, isLoading: boolean, action: string) {
        if (isLoading) {
            // Save original text
            const originalText = button.textContent || 'Submit';
            button.setAttribute('data-original-text', originalText);
            
            // Set loading text
            const loadingText = this.getCustomLoadingMessage(action) || 'Please wait...';
            button.textContent = loadingText;
        } else {
            button.textContent = button.getAttribute('data-original-text') || 'Submit';
        }
    }

    private getCustomLoadingMessage(action: string): string | undefined {
        switch(action) {
            case 'signin':
                return this.config.messages?.signinLoading;
            case 'signup':
                return this.config.messages?.signupLoading;
            case 'reset':
                return this.config.messages?.resetLoading;
            case 'update':
                return this.config.messages?.updateLoading;
            case 'resend':
                return this.config.messages?.resendLoading;
            default:
                return undefined;
        }
    }

    private injectStyles(): void {
        if (this.config.disableStyles) {
            return;
        }

        const existingStyle = document.getElementById('authiqa-styles');
        if (existingStyle) {
            existingStyle.remove();
        }

        const style = document.createElement('style');
        style.id = 'authiqa-styles';

        // Get the effective theme
        const effectiveTheme: 'light' | 'dark' = 
            (!this.config.theme || this.config.theme === 'none') ? 'light' : this.config.theme;

        let styleContent = '';

        // Add base theme styles
        styleContent += getStyleContent(effectiveTheme);

        // Add component styles
        const componentStyles = getComponentStyles(effectiveTheme);
        styleContent += `
            /* Modal Styles */
            .authiqa-modal-overlay {
                ${Object.entries(componentStyles.modal.overlay)
                    .map(([key, value]) => `${key}: ${value};`)
                    .join('\n')}
            }
            .authiqa-modal-container {
                ${Object.entries(componentStyles.modal.container)
                    .map(([key, value]) => `${key}: ${value};`)
                    .join('\n')}
            }
            .authiqa-iframe {
                ${Object.entries(componentStyles.iframe)
                    .map(([key, value]) => `${key}: ${value};`)
                    .join('\n')}
            }
            /* Message Styles */
            .authiqa-message {
                ${Object.entries(componentStyles.message)
                    .map(([key, value]) => `${key}: ${value};`)
                    .join('\n')}
            }
            .authiqa-message-success {
                ${Object.entries(componentStyles.messageSuccess)
                    .map(([key, value]) => `${key}: ${value};`)
                    .join('\n')}
            }
            .authiqa-message-error {
                ${Object.entries(componentStyles.messageError)
                    .map(([key, value]) => `${key}: ${value};`)
                    .join('\n')}
            }
            .authiqa-message-show {
                ${Object.entries(componentStyles.messageShow)
                    .map(([key, value]) => `${key}: ${value};`)
                    .join('\n')}
            }
        `;

        // Add custom styles if customization is provided
        if (this.config.customization) {
            const styleGenerator = new StyleGenerator(this.config.customization);
            styleContent += styleGenerator.generateStyles();
            
            // Add page layout styles
            styleContent += generatePageLayoutStyles(this.config);
            
            // Add terms container styles
            styleContent += generateTermsContainerStyles(this.config);
        }

        style.textContent = styleContent;
        document.head.appendChild(style);
    }

    private generateCustomStyles(customization: any): string {
        const { colors, typography, layout, buttons } = customization;
        
        return `
            .authiqa-container {
                background-color: ${colors.background};
                padding: ${layout.padding};
                margin: ${layout.margin};
                border-radius: ${layout.borderRadius};
                max-width: ${layout.maxWidth};
                font-family: ${typography.fontFamily};
            }

            .authiqa-container h1 {
                color: ${typography.titleColor};
                font-size: ${typography.titleSize};
            }

            .authiqa-container input {
                background-color: ${colors.inputBackground};
                color: ${colors.inputText};
                border: 1px solid ${colors.borderColor};
            }

            .authiqa-container button {
                background-color: ${colors.buttonBackground};
                color: ${colors.buttonText};
                height: ${buttons.height || '40px'};
                width: ${buttons.width || '100%'};
                border-radius: ${buttons.borderRadius};
            }
        `;
    }

    private updateTheme(newTheme: 'light' | 'dark'): void {
        if (this.config.disableStyles) return;

        const styleElement = document.getElementById('authiqa-styles');
        if (!styleElement) {
            this.injectStyles(); // Re-inject if missing
            return;
        }

        // Update body theme
        if (newTheme === 'dark') {
            document.body.setAttribute('data-theme', 'dark');
        } else {
            document.body.removeAttribute('data-theme');
        }

        // Update container theme if it exists
        const container = document.getElementById(this.config.container);
        if (container) {
            container.setAttribute('data-theme', newTheme);
        }
    }

    public cleanup(): void {
        // Remove injected styles
        const styleElement = document.getElementById('authiqa-styles');
        if (styleElement) {
            styleElement.remove();
        }

        // Reset body styles
        document.body.style.backgroundColor = '';
        document.body.style.display = '';
        document.body.style.minHeight = '';
        document.body.style.alignItems = '';
        document.body.style.justifyContent = '';

        // Remove theme attributes
        document.body.removeAttribute('data-theme');
        const container = document.getElementById(this.config.container);
        if (container) {
            container.removeAttribute('data-theme');
            // Reset container styles
            container.style.marginTop = '';
            container.style.marginBottom = '';
            container.style.marginLeft = '';
            container.style.marginRight = '';
        }
    }

    private handleApiError(error: any): void {
        if (error?.error?.message) {
            // If it's an API error response, show the original message
            this.showMessage(error.error.message, 'error');
        } else if (error instanceof Error) {
            // Only for network errors
            this.showMessage('Unable to connect to the server', 'error');
        } else {
            // Fallback error
            this.showMessage('An unexpected error occurred', 'error');
        }
    }

    private validateDomain(organizationUrl: string): boolean {
        // Check for development mode
        if (this.isDevelopmentMode()) {
            return true;
        }
        
        // Extract domain from organization URL
        let orgDomain;
        try {
            orgDomain = new URL(organizationUrl).hostname;
        } catch (e) {
            console.error('Invalid organization URL:', organizationUrl);
            return false;
        }
        
        // Get current domain
        const currentDomain = window.location.hostname;
        
        // Check if current domain matches organization domain, is a subdomain,
        // or is an Authiqa domain
        return currentDomain === orgDomain || 
               currentDomain.endsWith('.' + orgDomain) ||
               currentDomain === 'authiqa.com' || 
               currentDomain === 'www.authiqa.com';
    }

    private isDevelopmentMode(): boolean {
        const scriptElement = document.querySelector('script[data-public-key]');
        if (!scriptElement) return false;
        
        const devMode = scriptElement.getAttribute('authiqa--dev-data-mode');
        return devMode === 'true';
    }

    private showUnauthorizedError(): void {
        const container = document.getElementById(this.config.container);
        if (!container) return;
        
        // Clear any existing content
        container.innerHTML = '';
        
        // Create error message
        const errorDiv = document.createElement('div');
        errorDiv.className = 'authiqa-error-container';
        
        const errorTitle = document.createElement('h2');
        errorTitle.textContent = 'Unauthorized Domain';
        errorTitle.style.color = '#e74c3c'; // Red color for error title
        
        const errorMessage = document.createElement('p');
        errorMessage.textContent = 'This widget can only be used on authorized domains. Please visit Authiqa and signin to update your organization related information';
        errorMessage.style.color = '#333333'; // Dark gray for message text
        
        const link = document.createElement('a');
        link.href = 'https://authiqa.com';
        link.textContent = 'Visit Authiqa';
        link.style.color = '#3498db'; // Blue for link
        
        errorDiv.appendChild(errorTitle);
        errorDiv.appendChild(errorMessage);
        errorDiv.appendChild(link);
        container.appendChild(errorDiv);
        
        // Add some basic styling
        const style = document.createElement('style');
        style.textContent = `
            .authiqa-error-container {
                padding: 20px;
                border: 1px solid #e74c3c;
                border-radius: 5px;
                background-color: #fef5f5;
                text-align: center;
                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            }
            .authiqa-error-container h2 {
                color: #e74c3c;
                margin-top: 0;
            }
            .authiqa-error-container p {
                color: #333333;
                margin-bottom: 15px;
            }
            .authiqa-error-container a {
                display: inline-block;
                margin-top: 15px;
                color: #3498db;
                text-decoration: none;
            }
            .authiqa-error-container a:hover {
                text-decoration: underline;
            }
        `;
        document.head.appendChild(style);
    }
}

// Register the widget on the window object
window.AuthiqaWidget = AuthiqaWidget;

// Then initialize
document.addEventListener('DOMContentLoaded', () => {
    try {
        const scriptElement = document.querySelector('script[data-public-key]'); // Changed from data-api-key
        
        if (!scriptElement) {
            console.error("Script tag with data-public-key not found."); // Changed error message
            return;
        }

        const publicKey = scriptElement.getAttribute('data-public-key'); // Changed from data-api-key
        
        // Get action from script attribute first
        let action = scriptElement.getAttribute('action');
        
        // If no action is provided in the script tag, check the entire URL
        if (!action) {
            const fullUrl = window.location.href;
            
            // List of valid actions to check for
            const validActions = ['signin', 'signup', 'verify', 'reset', 'update', 'resend'];
            
            // Find the first valid action that appears in the URL
            const foundAction = validActions.find(act => fullUrl.includes(act));
            
            if (foundAction) {
                action = foundAction;
            } else {
                // Default to signin if no valid action is found
                action = 'signin';
            }
        }
        
        const termsAndConditions = scriptElement.getAttribute('termsAndConditions');
        const privacy = scriptElement.getAttribute('privacy');
        const notificationSettings = scriptElement.getAttribute('notificationSettings');
        const theme = scriptElement.getAttribute('theme') || 'light';
        const disableStyles = scriptElement.getAttribute('disable-styles') === 'true';
        const verifyAuthPath = scriptElement.getAttribute('verifyAuthPath');
        const updatePasswordPath = scriptElement.getAttribute('updatePasswordPath');
        const resendAuthPath = scriptElement.getAttribute('resendAuthPath');
        const successAuthPath = scriptElement.getAttribute('successAuthPath');
        const signinAuthPath = scriptElement.getAttribute('signinAuthPath');

        // Parse custom messages
        let messages;
        const messagesAttr = scriptElement.getAttribute('data-messages');
        if (messagesAttr) {
            try {
                messages = JSON.parse(messagesAttr);
            } catch (error) {
                console.error('Failed to parse custom messages:', error);
            }
        }

        // Parse customization
        let customization;
        const customizationAttr = scriptElement.getAttribute('data-customization');
        if (customizationAttr) {
            try {
                customization = JSON.parse(customizationAttr);
            } catch (error) {
                console.error('Failed to parse customization:', error);
            }
        }

        

        if (typeof window.AuthiqaWidget !== 'function') {
            console.error('AuthiqaWidget not properly registered');
            return;
        }

        const widgetConfig = {
            publicKey: publicKey || '', // Changed from apiKey
            container: 'authiqa',
            mode: 'popup',
            theme: theme as 'light' | 'dark' | 'none',
            disableStyles,
            organizationDomain: 'authiqa.com',
            termsAndConditions,
            privacy,
            notificationSettings,
            messages,
            customization, // Add this line
            verifyAuthPath,
            updatePasswordPath,
            resendAuthPath,
            successAuthPath,
            signinAuthPath,
        };

        const widget = new window.AuthiqaWidget(widgetConfig);
        
        // Show form immediately, don't wait for initialize
        widget.show(action);
        
    } catch (error) {
        console.error('Error during widget initialization:', error);
    }
});
